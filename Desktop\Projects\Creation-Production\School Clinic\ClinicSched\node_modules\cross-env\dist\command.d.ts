/**
 * Converts an environment variable usage to be appropriate for the current OS
 * @param command Command to convert
 * @param env Map of the current environment variable names and their values
 * @param normalize If the command should be normalized using `path` after converting
 * @returns Converted command
 */
export declare function commandConvert(command: string, env: NodeJS.ProcessEnv, normalize?: boolean): string;
