import type { Config } from "tailwindcss";

export default {
  darkMode: ["class"],
  content: ["./client/index.html", "./client/src/**/*.{js,jsx,ts,tsx}"],
  theme: {
    extend: {
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      colors: {
        background: "var(--background)",
        foreground: "var(--foreground)",
        card: {
          DEFAULT: "var(--card)",
          foreground: "var(--card-foreground)",
        },
        popover: {
          DEFAULT: "var(--popover)",
          foreground: "var(--popover-foreground)",
        },
        primary: {
          DEFAULT: "var(--primary)",
          foreground: "var(--primary-foreground)",
        },
        secondary: {
          DEFAULT: "var(--secondary)",
          foreground: "var(--secondary-foreground)",
        },
        muted: {
          DEFAULT: "var(--muted)",
          foreground: "var(--muted-foreground)",
        },
        accent: {
          DEFAULT: "var(--accent)",
          foreground: "var(--accent-foreground)",
        },
        destructive: {
          DEFAULT: "var(--destructive)",
          foreground: "var(--destructive-foreground)",
        },
        border: "var(--border)",
        input: "var(--input)",
        ring: "var(--ring)",
        chart: {
          "1": "var(--chart-1)",
          "2": "var(--chart-2)",
          "3": "var(--chart-3)",
          "4": "var(--chart-4)",
          "5": "var(--chart-5)",
        },
        sidebar: {
          DEFAULT: "var(--sidebar)",
          foreground: "var(--sidebar-foreground)",
          primary: "var(--sidebar-primary)",
          "primary-foreground": "var(--sidebar-primary-foreground)",
          accent: "var(--sidebar-accent)",
          "accent-foreground": "var(--sidebar-accent-foreground)",
          border: "var(--sidebar-border)",
          ring: "var(--sidebar-ring)",
        },
        // Medical-specific color extensions
        medical: {
          primary: "var(--primary)",
          success: "hsl(142.1 76.2% 36.3%)",
          warning: "hsl(45.4 93.4% 47.5%)",
          danger: "hsl(0 84.2% 60.2%)",
          info: "hsl(221.2 83.2% 53.3%)",
        },
        status: {
          available: "hsl(142.1 76.2% 36.3%)",
          busy: "hsl(0 84.2% 60.2%)",
          break: "hsl(45.4 93.4% 47.5%)",
          offline: "hsl(215.4 16.3% 46.9%)",
        },
        inventory: {
          good: "hsl(142.1 76.2% 36.3%)",
          low: "hsl(45.4 93.4% 47.5%)",
          out: "hsl(0 84.2% 60.2%)",
        },
      },
      fontFamily: {
        sans: ["var(--font-sans)"],
        serif: ["var(--font-serif)"],
        mono: ["var(--font-mono)"],
      },
      keyframes: {
        "accordion-down": {
          from: {
            height: "0",
          },
          to: {
            height: "var(--radix-accordion-content-height)",
          },
        },
        "accordion-up": {
          from: {
            height: "var(--radix-accordion-content-height)",
          },
          to: {
            height: "0",
          },
        },
        "pulse-medical": {
          "0%, 100%": {
            opacity: "1",
          },
          "50%": {
            opacity: ".5",
          },
        },
        "fade-in": {
          "0%": {
            opacity: "0",
            transform: "translateY(10px)",
          },
          "100%": {
            opacity: "1",
            transform: "translateY(0)",
          },
        },
        "slide-in": {
          "0%": {
            transform: "translateX(-100%)",
          },
          "100%": {
            transform: "translateX(0)",
          },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "pulse-medical": "pulse-medical 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",
        "fade-in": "fade-in 0.3s ease-out",
        "slide-in": "slide-in 0.3s ease-out",
      },
      spacing: {
        "18": "4.5rem",
        "88": "22rem",
        "128": "32rem",
      },
      maxWidth: {
        "8xl": "88rem",
        "9xl": "96rem",
      },
      zIndex: {
        "60": "60",
        "70": "70",
        "80": "80",
        "90": "90",
        "100": "100",
      },
      backdropBlur: {
        "xs": "2px",
      },
      boxShadow: {
        "medical": "0 4px 6px -1px rgba(59, 130, 246, 0.1), 0 2px 4px -1px rgba(59, 130, 246, 0.06)",
        "medical-lg": "0 10px 15px -3px rgba(59, 130, 246, 0.1), 0 4px 6px -2px rgba(59, 130, 246, 0.05)",
      },
      // Medical-specific grid templates
      gridTemplateColumns: {
        "medical-sidebar": "256px 1fr",
        "medical-calendar": "repeat(7, 1fr)",
        "medical-schedule": "200px repeat(5, 1fr)",
      },
      // Medical-specific breakpoints for responsive design
      screens: {
        "xs": "475px",
        "3xl": "1680px",
      },
    },
  },
  plugins: [
    require("tailwindcss-animate"), 
    require("@tailwindcss/typography"),
    // Custom plugin for medical-specific utilities
    function({ addUtilities }: any) {
      const medicalUtilities = {
        '.medical-card': {
          'border-left': '4px solid var(--primary)',
          'background-color': 'var(--card)',
          'padding': '1rem',
          'border-radius': 'var(--radius)',
          'border': '1px solid var(--border)',
        },
        '.medical-badge-scheduled': {
          'background-color': 'hsl(221.2 83.2% 95%)',
          'color': 'hsl(221.2 83.2% 53.3%)',
        },
        '.medical-badge-completed': {
          'background-color': 'hsl(142.1 76.2% 95%)',
          'color': 'hsl(142.1 76.2% 36.3%)',
        },
        '.medical-badge-cancelled': {
          'background-color': 'hsl(0 84.2% 95%)',
          'color': 'hsl(0 84.2% 60.2%)',
        },
        '.medical-form-section': {
          'padding': '1rem',
          'border': '1px solid var(--border)',
          'border-radius': 'var(--radius)',
          'background-color': 'var(--card)',
        },
        '.sidebar-transition': {
          'transition': 'transform 0.3s ease-in-out',
        },
        '.calendar-day': {
          'aspect-ratio': '1',
          'padding': '0.5rem',
          'text-align': 'center',
          'border': '1px solid var(--border)',
          'background-color': 'var(--card)',
          'transition': 'all 0.2s ease-in-out',
        },
        '.calendar-day:hover': {
          'background-color': 'var(--muted)',
        },
        '.calendar-day-selected': {
          'background-color': 'var(--primary)',
          'color': 'var(--primary-foreground)',
        },
        '.time-slot': {
          'padding': '0.75rem',
          'border': '1px solid var(--border)',
          'border-radius': 'var(--radius)',
          'text-align': 'center',
          'background-color': 'var(--card)',
          'transition': 'all 0.2s ease-in-out',
          'cursor': 'pointer',
        },
        '.time-slot:hover': {
          'background-color': 'var(--primary)',
          'color': 'var(--primary-foreground)',
        },
      };
      
      addUtilities(medicalUtilities);
    },
  ],
} satisfies Config;
