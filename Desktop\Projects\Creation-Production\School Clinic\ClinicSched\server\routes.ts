import type { Express } from "express";
import { createServer, type Server } from "http";
import { setupAuth } from "./auth";
import { storage } from "./storage";
import { insertAppointmentSchema, insertMedicalRecordSchema, insertStaffScheduleSchema, insertInventorySchema } from "@shared/schema";

export function registerRoutes(app: Express): Server {
  // Setup authentication routes
  setupAuth(app);

  // Appointment routes
  app.post("/api/appointments", async (req, res, next) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Authentication required" });
      }

      const appointmentData = insertAppointmentSchema.parse(req.body);
      const appointment = await storage.createAppointment(appointmentData);
      res.status(201).json(appointment);
    } catch (error) {
      next(error);
    }
  });

  app.get("/api/appointments/patient/:patientId", async (req, res, next) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Authentication required" });
      }

      const { patientId } = req.params;
      
      // Users can only see their own appointments unless they're staff/admin
      if (req.user?.role === "student" && req.user.id !== patientId) {
        return res.status(403).json({ message: "Access denied" });
      }

      const appointments = await storage.getAppointmentsByPatient(patientId);
      res.json(appointments);
    } catch (error) {
      next(error);
    }
  });

  app.get("/api/appointments/provider/:providerId", async (req, res, next) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Authentication required" });
      }

      if (req.user?.role === "student") {
        return res.status(403).json({ message: "Access denied" });
      }

      const { providerId } = req.params;
      const appointments = await storage.getAppointmentsByProvider(providerId);
      res.json(appointments);
    } catch (error) {
      next(error);
    }
  });

  app.get("/api/appointments/date/:date", async (req, res, next) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Authentication required" });
      }

      const { date } = req.params;
      const appointmentDate = new Date(date);
      const appointments = await storage.getAppointmentsByDate(appointmentDate);
      res.json(appointments);
    } catch (error) {
      next(error);
    }
  });

  app.put("/api/appointments/:id", async (req, res, next) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Authentication required" });
      }

      const { id } = req.params;
      const updates = req.body;
      const appointment = await storage.updateAppointment(id, updates);
      res.json(appointment);
    } catch (error) {
      next(error);
    }
  });

  app.post("/api/appointments/:id/checkin", async (req, res, next) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Authentication required" });
      }

      const { id } = req.params;
      const appointment = await storage.checkInAppointment(id);
      res.json(appointment);
    } catch (error) {
      next(error);
    }
  });

  // Medical Records routes
  app.post("/api/medical-records", async (req, res, next) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Authentication required" });
      }

      if (req.user?.role === "student") {
        return res.status(403).json({ message: "Access denied" });
      }

      const recordData = insertMedicalRecordSchema.parse(req.body);
      const record = await storage.createMedicalRecord(recordData);
      res.status(201).json(record);
    } catch (error) {
      next(error);
    }
  });

  app.get("/api/medical-records/patient/:patientId", async (req, res, next) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Authentication required" });
      }

      const { patientId } = req.params;
      
      // Users can only see their own records unless they're staff/admin
      if (req.user?.role === "student" && req.user.id !== patientId) {
        return res.status(403).json({ message: "Access denied" });
      }

      const records = await storage.getMedicalRecordsByPatient(patientId);
      res.json(records);
    } catch (error) {
      next(error);
    }
  });

  // Staff schedule routes
  app.post("/api/staff-schedules", async (req, res, next) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Authentication required" });
      }

      if (req.user?.role === "student") {
        return res.status(403).json({ message: "Access denied" });
      }

      const scheduleData = insertStaffScheduleSchema.parse(req.body);
      const schedule = await storage.createStaffSchedule(scheduleData);
      res.status(201).json(schedule);
    } catch (error) {
      next(error);
    }
  });

  app.get("/api/staff-schedules/:staffId", async (req, res, next) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Authentication required" });
      }

      const { staffId } = req.params;
      const schedules = await storage.getStaffSchedule(staffId);
      res.json(schedules);
    } catch (error) {
      next(error);
    }
  });

  // Inventory routes
  app.get("/api/inventory", async (req, res, next) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Authentication required" });
      }

      if (req.user?.role === "student") {
        return res.status(403).json({ message: "Access denied" });
      }

      const inventory = await storage.getInventory();
      res.json(inventory);
    } catch (error) {
      next(error);
    }
  });

  app.post("/api/inventory", async (req, res, next) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Authentication required" });
      }

      if (req.user?.role !== "admin") {
        return res.status(403).json({ message: "Admin access required" });
      }

      const itemData = insertInventorySchema.parse(req.body);
      const item = await storage.createInventoryItem(itemData);
      res.status(201).json(item);
    } catch (error) {
      next(error);
    }
  });

  // Staff members list
  app.get("/api/staff", async (req, res, next) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Authentication required" });
      }

      const staff = await storage.getStaffMembers();
      res.json(staff);
    } catch (error) {
      next(error);
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
