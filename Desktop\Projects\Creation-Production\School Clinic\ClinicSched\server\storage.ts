import { 
  users, 
  appointments, 
  medicalRecords, 
  staffSchedules, 
  inventory,
  type User, 
  type InsertUser,
  type Appointment,
  type InsertAppointment,
  type MedicalRecord,
  type InsertMedicalRecord,
  type StaffSchedule,
  type InsertStaffSchedule,
  type Inventory,
  type InsertInventory
} from "@shared/schema";
import { db } from "./db";
import { eq, and, gte, lte, desc, asc } from "drizzle-orm";
import session from "express-session";
import connectPg from "connect-pg-simple";
import { pool } from "./db";

const PostgresSessionStore = connectPg(session);

export interface IStorage {
  // User management
  getUser(id: string): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  updateUser(id: string, updates: Partial<InsertUser>): Promise<User>;
  getStaffMembers(): Promise<User[]>;

  // Appointments
  createAppointment(appointment: InsertAppointment): Promise<Appointment>;
  getAppointment(id: string): Promise<Appointment | undefined>;
  getAppointmentsByPatient(patientId: string): Promise<Appointment[]>;
  getAppointmentsByProvider(providerId: string): Promise<Appointment[]>;
  getAppointmentsByDate(date: Date): Promise<Appointment[]>;
  updateAppointment(id: string, updates: Partial<InsertAppointment>): Promise<Appointment>;
  checkInAppointment(id: string): Promise<Appointment>;

  // Medical Records
  createMedicalRecord(record: InsertMedicalRecord): Promise<MedicalRecord>;
  getMedicalRecordsByPatient(patientId: string): Promise<MedicalRecord[]>;
  getMedicalRecord(id: string): Promise<MedicalRecord | undefined>;
  updateMedicalRecord(id: string, updates: Partial<InsertMedicalRecord>): Promise<MedicalRecord>;

  // Staff Schedules
  createStaffSchedule(schedule: InsertStaffSchedule): Promise<StaffSchedule>;
  getStaffSchedule(staffId: string): Promise<StaffSchedule[]>;
  updateStaffSchedule(id: string, updates: Partial<InsertStaffSchedule>): Promise<StaffSchedule>;
  deleteStaffSchedule(id: string): Promise<void>;

  // Inventory
  createInventoryItem(item: InsertInventory): Promise<Inventory>;
  getInventory(): Promise<Inventory[]>;
  getInventoryItem(id: string): Promise<Inventory | undefined>;
  updateInventoryItem(id: string, updates: Partial<InsertInventory>): Promise<Inventory>;
  deleteInventoryItem(id: string): Promise<void>;

  sessionStore: session.SessionStore;
}

export class DatabaseStorage implements IStorage {
  public sessionStore: session.SessionStore;

  constructor() {
    this.sessionStore = new PostgresSessionStore({ 
      pool, 
      createTableIfMissing: true 
    });
  }

  // User management
  async getUser(id: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user || undefined;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user || undefined;
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.email, email));
    return user || undefined;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const [user] = await db
      .insert(users)
      .values(insertUser)
      .returning();
    return user;
  }

  async updateUser(id: string, updates: Partial<InsertUser>): Promise<User> {
    const [user] = await db
      .update(users)
      .set(updates)
      .where(eq(users.id, id))
      .returning();
    return user;
  }

  async getStaffMembers(): Promise<User[]> {
    return await db
      .select()
      .from(users)
      .where(eq(users.role, "staff"))
      .orderBy(asc(users.firstName));
  }

  // Appointments
  async createAppointment(appointment: InsertAppointment): Promise<Appointment> {
    const [newAppointment] = await db
      .insert(appointments)
      .values(appointment)
      .returning();
    return newAppointment;
  }

  async getAppointment(id: string): Promise<Appointment | undefined> {
    const [appointment] = await db
      .select()
      .from(appointments)
      .where(eq(appointments.id, id));
    return appointment || undefined;
  }

  async getAppointmentsByPatient(patientId: string): Promise<Appointment[]> {
    return await db
      .select()
      .from(appointments)
      .where(eq(appointments.patientId, patientId))
      .orderBy(desc(appointments.appointmentDate));
  }

  async getAppointmentsByProvider(providerId: string): Promise<Appointment[]> {
    return await db
      .select()
      .from(appointments)
      .where(eq(appointments.providerId, providerId))
      .orderBy(asc(appointments.appointmentDate));
  }

  async getAppointmentsByDate(date: Date): Promise<Appointment[]> {
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);

    return await db
      .select()
      .from(appointments)
      .where(
        and(
          gte(appointments.appointmentDate, startOfDay),
          lte(appointments.appointmentDate, endOfDay)
        )
      )
      .orderBy(asc(appointments.appointmentDate));
  }

  async updateAppointment(id: string, updates: Partial<InsertAppointment>): Promise<Appointment> {
    const [appointment] = await db
      .update(appointments)
      .set(updates)
      .where(eq(appointments.id, id))
      .returning();
    return appointment;
  }

  async checkInAppointment(id: string): Promise<Appointment> {
    const [appointment] = await db
      .update(appointments)
      .set({ 
        checkedIn: true, 
        checkedInAt: new Date() 
      })
      .where(eq(appointments.id, id))
      .returning();
    return appointment;
  }

  // Medical Records
  async createMedicalRecord(record: InsertMedicalRecord): Promise<MedicalRecord> {
    const [newRecord] = await db
      .insert(medicalRecords)
      .values(record)
      .returning();
    return newRecord;
  }

  async getMedicalRecordsByPatient(patientId: string): Promise<MedicalRecord[]> {
    return await db
      .select()
      .from(medicalRecords)
      .where(eq(medicalRecords.patientId, patientId))
      .orderBy(desc(medicalRecords.visitDate));
  }

  async getMedicalRecord(id: string): Promise<MedicalRecord | undefined> {
    const [record] = await db
      .select()
      .from(medicalRecords)
      .where(eq(medicalRecords.id, id));
    return record || undefined;
  }

  async updateMedicalRecord(id: string, updates: Partial<InsertMedicalRecord>): Promise<MedicalRecord> {
    const [record] = await db
      .update(medicalRecords)
      .set(updates)
      .where(eq(medicalRecords.id, id))
      .returning();
    return record;
  }

  // Staff Schedules
  async createStaffSchedule(schedule: InsertStaffSchedule): Promise<StaffSchedule> {
    const [newSchedule] = await db
      .insert(staffSchedules)
      .values(schedule)
      .returning();
    return newSchedule;
  }

  async getStaffSchedule(staffId: string): Promise<StaffSchedule[]> {
    return await db
      .select()
      .from(staffSchedules)
      .where(eq(staffSchedules.staffId, staffId))
      .orderBy(asc(staffSchedules.dayOfWeek));
  }

  async updateStaffSchedule(id: string, updates: Partial<InsertStaffSchedule>): Promise<StaffSchedule> {
    const [schedule] = await db
      .update(staffSchedules)
      .set(updates)
      .where(eq(staffSchedules.id, id))
      .returning();
    return schedule;
  }

  async deleteStaffSchedule(id: string): Promise<void> {
    await db
      .delete(staffSchedules)
      .where(eq(staffSchedules.id, id));
  }

  // Inventory
  async createInventoryItem(item: InsertInventory): Promise<Inventory> {
    const [newItem] = await db
      .insert(inventory)
      .values(item)
      .returning();
    return newItem;
  }

  async getInventory(): Promise<Inventory[]> {
    return await db
      .select()
      .from(inventory)
      .orderBy(asc(inventory.itemName));
  }

  async getInventoryItem(id: string): Promise<Inventory | undefined> {
    const [item] = await db
      .select()
      .from(inventory)
      .where(eq(inventory.id, id));
    return item || undefined;
  }

  async updateInventoryItem(id: string, updates: Partial<InsertInventory>): Promise<Inventory> {
    const [item] = await db
      .update(inventory)
      .set({ ...updates, lastUpdated: new Date() })
      .where(eq(inventory.id, id))
      .returning();
    return item;
  }

  async deleteInventoryItem(id: string): Promise<void> {
    await db
      .delete(inventory)
      .where(eq(inventory.id, id));
  }
}

export const storage = new DatabaseStorage();
